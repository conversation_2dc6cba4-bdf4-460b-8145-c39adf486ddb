<?php
/**
 * Dashboard template for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle messages
$message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
?>

<div class="wrap wcm-dashboard">
    <h1 class="wp-heading-inline"><?php _e('Cash Manager Dashboard', 'woo-cash-manager'); ?></h1>
    <button type="button" class="page-title-action" id="wcm-add-expense-btn"><?php _e('Add Expense', 'woo-cash-manager'); ?></button>
    <hr class="wp-header-end">
    
    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible">
            <p>
                <?php
                switch ($message) {
                    case 'added':
                        _e('Expense added successfully.', 'woo-cash-manager');
                        break;
                    case 'updated':
                        _e('Expense updated successfully.', 'woo-cash-manager');
                        break;
                    case 'deleted':
                        _e('Expense deleted successfully.', 'woo-cash-manager');
                        break;
                }
                ?>
            </p>
        </div>
    <?php endif; ?>
    
    <!-- Cash Balance Cards -->
    <div class="wcm-cards-container" style="animation-delay: 0.1s;">
        <div class="wcm-card wcm-card-balance <?php echo $cash_data['balance'] >= 0 ? 'positive' : 'negative'; ?>">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-chart-line"></span><?php _e('Current Cash Balance', 'woo-cash-manager'); ?></h3>
                <span class="wcm-refresh-btn" id="wcm-refresh-dashboard" title="<?php _e('Refresh Data', 'woo-cash-manager'); ?>">
                    <span class="dashicons dashicons-update"></span>
                </span>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-balance">
                    <?php echo wc_price($cash_data['balance']); ?>
                </div>
                <div class="wcm-status">
                    <?php if ($cash_data['balance'] >= 0): ?>
                        <span class="wcm-status-positive"><?php _e('Positive Balance', 'woo-cash-manager'); ?></span>
                    <?php else: ?>
                        <span class="wcm-status-negative"><?php _e('Negative Balance', 'woo-cash-manager'); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="wcm-card wcm-card-income">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-arrow-up-alt"></span><?php _e('Total Income', 'woo-cash-manager'); ?></h3>
                <span class="wcm-info-icon" title="<?php _e('From completed and processing WooCommerce orders', 'woo-cash-manager'); ?>">
                    <span class="dashicons dashicons-info"></span>
                </span>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-income">
                    <?php echo wc_price($cash_data['income']); ?>
                </div>
                <div class="wcm-subtitle">
                    <?php _e('WooCommerce Orders', 'woo-cash-manager'); ?>
                </div>
            </div>
        </div>
        
        <div class="wcm-card wcm-card-expenses">
            <div class="wcm-card-header">
                <h3><span class="dashicons dashicons-arrow-down-alt"></span><?php _e('Total Expenses', 'woo-cash-manager'); ?></h3>
                <a href="<?php echo admin_url('admin.php?page=woo-cash-expenses'); ?>" class="wcm-view-link">
                    <?php _e('View All', 'woo-cash-manager'); ?>
                </a>
            </div>
            <div class="wcm-card-content">
                <div class="wcm-amount" id="wcm-expenses">
                    <?php echo wc_price($cash_data['expenses']); ?>
                </div>
                <div class="wcm-subtitle">
                    <?php _e('Manual Entries', 'woo-cash-manager'); ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chart Section -->
    <div class="wcm-chart-section">
        <div class="wcm-chart-header">
            <h2><?php _e('Income vs Expenses Chart', 'woo-cash-manager'); ?></h2>
            <div class="wcm-chart-controls">
                <select id="wcm-chart-period">
                    <option value="monthly"><?php _e('Monthly (12 months)', 'woo-cash-manager'); ?></option>
                    <option value="quarterly"><?php _e('Quarterly (8 quarters)', 'woo-cash-manager'); ?></option>
                    <option value="yearly"><?php _e('Yearly (5 years)', 'woo-cash-manager'); ?></option>
                </select>
                <button type="button" class="button" id="wcm-update-chart"><?php _e('Update Chart', 'woo-cash-manager'); ?></button>
            </div>
        </div>
        <div class="wcm-chart-container">
            <canvas id="wcm-chart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="wcm-quick-actions">
        <h2><?php _e('Quick Actions', 'woo-cash-manager'); ?></h2>
        <div class="wcm-actions-grid">
            <button type="button" class="wcm-action-card wcm-add-expense-trigger">
                <span class="dashicons dashicons-plus-alt"></span>
                <h3><?php _e('Add Expense', 'woo-cash-manager'); ?></h3>
                <p><?php _e('Record a new business expense', 'woo-cash-manager'); ?></p>
            </button>

            <button type="button" class="wcm-action-card" onclick="document.getElementById('wcm-all-expenses').scrollIntoView({behavior: 'smooth'})">
                <span class="dashicons dashicons-list-view"></span>
                <h3><?php _e('View All Expenses', 'woo-cash-manager'); ?></h3>
                <p><?php _e('Scroll to expenses list below', 'woo-cash-manager'); ?></p>
            </button>
            
            <a href="<?php echo admin_url('admin.php?page=wc-orders'); ?>" class="wcm-action-card">
                <span class="dashicons dashicons-cart"></span>
                <h3><?php _e('WooCommerce Orders', 'woo-cash-manager'); ?></h3>
                <p><?php _e('View your store orders', 'woo-cash-manager'); ?></p>
            </a>
            
            <a href="<?php echo admin_url('admin.php?page=wc-reports'); ?>" class="wcm-action-card">
                <span class="dashicons dashicons-chart-bar"></span>
                <h3><?php _e('WooCommerce Reports', 'woo-cash-manager'); ?></h3>
                <p><?php _e('Detailed sales reports', 'woo-cash-manager'); ?></p>
            </a>
        </div>
    </div>
    
    <!-- HPOS Status (for debugging) -->
    <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
        <div class="wcm-debug-info" style="background: #f0f0f1; padding: 10px; margin: 20px 0; border-radius: 4px; font-size: 12px;">
            <strong><?php _e('Debug Info:', 'woo-cash-manager'); ?></strong>
            <?php if (function_exists('wcm_is_hpos_enabled')): ?>
                HPOS Status: <?php echo wcm_is_hpos_enabled() ? 'Enabled ✅' : 'Disabled ❌'; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- All Expenses -->
    <div class="wcm-all-expenses" id="wcm-all-expenses">
        <div class="wcm-section-header">
            <h2><?php _e('All Expenses', 'woo-cash-manager'); ?></h2>
            <div class="wcm-expenses-actions">
                <button type="button" class="button button-primary wcm-add-expense-trigger">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php _e('Add New Expense', 'woo-cash-manager'); ?>
                </button>
                <button type="button" class="button button-secondary" id="wcm-refresh-expenses">
                    <span class="dashicons dashicons-update"></span>
                    <?php _e('Refresh', 'woo-cash-manager'); ?>
                </button>
            </div>
        </div>

        <!-- Expenses Summary -->
        <div class="wcm-expenses-summary">
            <div class="wcm-summary-item">
                <span class="wcm-summary-label"><?php _e('Total Expenses:', 'woo-cash-manager'); ?></span>
                <span class="wcm-summary-value"><?php echo wc_price($cash_data['expenses']); ?></span>
            </div>
            <div class="wcm-summary-item">
                <span class="wcm-summary-label"><?php _e('Total Entries:', 'woo-cash-manager'); ?></span>
                <span class="wcm-summary-value"><?php echo number_format($total_expenses); ?></span>
            </div>
        </div>
        
        <?php if ($expenses): ?>
            <div class="wcm-expenses-table-container">
                <table class="wp-list-table widefat fixed striped wcm-expenses-table">
                    <thead>
                        <tr>
                            <th class="wcm-col-title"><?php _e('Title', 'woo-cash-manager'); ?></th>
                            <th class="wcm-col-amount"><?php _e('Amount', 'woo-cash-manager'); ?></th>
                            <th class="wcm-col-category"><?php _e('Category', 'woo-cash-manager'); ?></th>
                            <th class="wcm-col-date"><?php _e('Date', 'woo-cash-manager'); ?></th>
                            <th class="wcm-col-actions"><?php _e('Actions', 'woo-cash-manager'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($expenses as $expense): ?>
                            <tr data-expense-id="<?php echo $expense->id; ?>">
                                <td class="wcm-col-title">
                                    <strong><?php echo esc_html($expense->title); ?></strong>
                                    <?php if ($expense->note): ?>
                                        <div class="wcm-expense-note"><?php echo esc_html($expense->note); ?></div>
                                    <?php endif; ?>
                                </td>
                                <td class="wcm-col-amount">
                                    <span class="wcm-amount"><?php echo wc_price($expense->amount); ?></span>
                                </td>
                                <td class="wcm-col-category">
                                    <span class="wcm-category-badge"><?php echo esc_html($expense->category); ?></span>
                                </td>
                                <td class="wcm-col-date">
                                    <span class="wcm-date"><?php echo date_i18n(get_option('date_format'), strtotime($expense->expense_date)); ?></span>
                                    <div class="wcm-created-date">
                                        <?php printf(__('Added: %s', 'woo-cash-manager'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($expense->created_at))); ?>
                                    </div>
                                </td>
                                <td class="wcm-col-actions">
                                    <div class="wcm-actions">
                                        <button type="button"
                                                class="button button-small wcm-edit-expense-btn"
                                                data-expense-id="<?php echo $expense->id; ?>"
                                                title="<?php _e('Edit Expense', 'woo-cash-manager'); ?>">
                                            <span class="dashicons dashicons-edit"></span>
                                            <?php _e('Edit', 'woo-cash-manager'); ?>
                                        </button>

                                        <button type="button"
                                                class="button button-small wcm-delete-btn"
                                                data-expense-id="<?php echo $expense->id; ?>"
                                                title="<?php _e('Delete Expense', 'woo-cash-manager'); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php _e('Delete', 'woo-cash-manager'); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="wcm-pagination">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'prev_text' => __('&laquo; Previous', 'woo-cash-manager'),
                        'next_text' => __('Next &raquo;', 'woo-cash-manager'),
                        'total' => $total_pages,
                        'current' => $current_page,
                        'show_all' => false,
                        'end_size' => 1,
                        'mid_size' => 2,
                        'type' => 'plain',
                    );

                    echo paginate_links($pagination_args);
                    ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <div class="wcm-no-data">
                <div class="wcm-no-data-icon">
                    <span class="dashicons dashicons-clipboard"></span>
                </div>
                <h3><?php _e('No expenses found', 'woo-cash-manager'); ?></h3>
                <p><?php _e('You haven\'t recorded any expenses yet. Start by adding your first expense!', 'woo-cash-manager'); ?></p>
                <div class="wcm-no-data-actions">
                    <button type="button" class="button button-primary wcm-add-expense-trigger">
                        <?php _e('Add Your First Expense', 'woo-cash-manager'); ?>
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add/Edit Expense Modal -->
<div id="wcm-expense-modal" class="wcm-modal" style="display: none;">
    <div class="wcm-modal-content wcm-expense-modal-content">
        <div class="wcm-modal-header">
            <h3 id="wcm-modal-title"><?php _e('Add New Expense', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body">
            <form id="wcm-expense-form" method="post" action="">
                <?php wp_nonce_field('wcm_add_expense', 'wcm_nonce'); ?>
                <input type="hidden" id="wcm-expense-id" name="expense_id" value="">
                <input type="hidden" id="wcm-form-action" name="wcm_add_expense" value="1">

                <div class="wcm-form-grid">
                    <!-- Title Field -->
                    <div class="wcm-form-group wcm-form-group-full">
                        <label for="wcm-modal-title-field" class="wcm-label">
                            <?php _e('Expense Title', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="text"
                               id="wcm-modal-title-field"
                               name="title"
                               class="wcm-input"
                               placeholder="<?php _e('e.g., Office supplies, Marketing campaign, etc.', 'woo-cash-manager'); ?>"
                               required>
                    </div>

                    <!-- Amount and Category Row -->
                    <div class="wcm-form-group">
                        <label for="wcm-modal-amount" class="wcm-label">
                            <?php _e('Amount', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <div class="wcm-input-wrapper">
                            <span class="wcm-currency-symbol"><?php echo get_woocommerce_currency_symbol(); ?></span>
                            <input type="number"
                                   id="wcm-modal-amount"
                                   name="amount"
                                   class="wcm-input wcm-amount-input"
                                   step="0.01"
                                   min="0.01"
                                   placeholder="0.00"
                                   required>
                        </div>
                    </div>

                    <div class="wcm-form-group">
                        <label for="wcm-modal-category" class="wcm-label">
                            <?php _e('Category', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="text"
                               id="wcm-modal-category"
                               name="category"
                               class="wcm-input"
                               placeholder="<?php _e('e.g., Marketing, Office, Travel', 'woo-cash-manager'); ?>"
                               list="wcm-modal-category-suggestions"
                               required>

                        <?php if (!empty($common_categories)): ?>
                            <datalist id="wcm-modal-category-suggestions">
                                <?php foreach ($common_categories as $category): ?>
                                    <option value="<?php echo esc_attr($category); ?>">
                                <?php endforeach; ?>
                            </datalist>
                        <?php endif; ?>
                    </div>

                    <!-- Date Field -->
                    <div class="wcm-form-group">
                        <label for="wcm-modal-date" class="wcm-label">
                            <?php _e('Expense Date', 'woo-cash-manager'); ?>
                            <span class="wcm-required">*</span>
                        </label>
                        <input type="date"
                               id="wcm-modal-date"
                               name="expense_date"
                               class="wcm-input"
                               value="<?php echo date('Y-m-d'); ?>"
                               max="<?php echo date('Y-m-d'); ?>"
                               required>
                    </div>

                    <!-- Note Field -->
                    <div class="wcm-form-group wcm-form-group-full">
                        <label for="wcm-modal-note" class="wcm-label">
                            <?php _e('Note (Optional)', 'woo-cash-manager'); ?>
                        </label>
                        <textarea id="wcm-modal-note"
                                  name="note"
                                  class="wcm-textarea"
                                  rows="3"
                                  placeholder="<?php _e('Add any additional details about this expense...', 'woo-cash-manager'); ?>"></textarea>
                    </div>
                </div>

                <!-- Quick Categories -->
                <?php if (!empty($common_categories)): ?>
                    <div class="wcm-modal-quick-categories">
                        <label class="wcm-label"><?php _e('Quick Categories:', 'woo-cash-manager'); ?></label>
                        <div class="wcm-category-buttons">
                            <?php foreach (array_slice($common_categories, 0, 8) as $category): ?>
                                <button type="button"
                                        class="button wcm-category-btn"
                                        data-category="<?php echo esc_attr($category); ?>">
                                    <?php echo esc_html($category); ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </form>
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-modal-cancel"><?php _e('Cancel', 'woo-cash-manager'); ?></button>
            <button type="submit" form="wcm-expense-form" class="button button-primary" id="wcm-modal-save">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php _e('Add Expense', 'woo-cash-manager'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="wcm-delete-modal" class="wcm-modal" style="display: none;">
    <div class="wcm-modal-content">
        <div class="wcm-modal-header">
            <h3><?php _e('Confirm Deletion', 'woo-cash-manager'); ?></h3>
            <span class="wcm-modal-close">&times;</span>
        </div>
        <div class="wcm-modal-body">
            <p><?php _e('Are you sure you want to delete this expense? This action cannot be undone.', 'woo-cash-manager'); ?></p>
        </div>
        <div class="wcm-modal-footer">
            <button type="button" class="button" id="wcm-cancel-delete"><?php _e('Cancel', 'woo-cash-manager'); ?></button>
            <button type="button" class="button button-primary" id="wcm-confirm-delete"><?php _e('Delete', 'woo-cash-manager'); ?></button>
        </div>
    </div>
</div>

<script type="text/javascript">
// Initialize chart data
var wcmChartData = <?php echo json_encode($monthly_data); ?>;
</script>
