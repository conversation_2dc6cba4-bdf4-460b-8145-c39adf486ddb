/**
 * Admin styles for Woo Cash Manager
 */

/* General Styles */
.wcm-dashboard,
.wcm-expenses-list,
.wcm-add-expense,
.wcm-edit-expense {
    max-width: 1200px;
}

/* Cards Container */
.wcm-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

/* Card Styles */
.wcm-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.wcm-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wcm-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.wcm-card:hover::before {
    opacity: 1;
}

.wcm-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f1;
}

.wcm-card-header h3 {
    margin: 0;
    font-size: 13px;
    font-weight: 700;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.wcm-card-header h3 .dashicons {
    font-size: 16px;
    margin-right: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-card-content {
    text-align: center;
}

.wcm-amount {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 12px;
    line-height: 1.1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.wcm-card-balance .wcm-amount {
    font-size: 36px;
    font-weight: 900;
}

.wcm-card-balance.positive .wcm-amount {
    background: linear-gradient(135deg, #00d4aa 0%, #00a32a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-card-balance.negative .wcm-amount {
    background: linear-gradient(135deg, #ff6b6b 0%, #d63638 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-card-income .wcm-amount {
    background: linear-gradient(135deg, #00d4aa 0%, #00a32a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-card-expenses .wcm-amount {
    background: linear-gradient(135deg, #ff6b6b 0%, #d63638 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-subtitle,
.wcm-status {
    font-size: 11px;
    color: #8e9aaf;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    margin-top: 4px;
}

.wcm-status-positive {
    color: #00a32a;
    font-weight: 700;
    background: rgba(0, 163, 42, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

.wcm-status-negative {
    color: #d63638;
    font-weight: 700;
    background: rgba(214, 54, 56, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-block;
}

/* Action Icons */
.wcm-refresh-btn,
.wcm-info-icon {
    cursor: pointer;
    color: #8e9aaf;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.wcm-refresh-btn:hover,
.wcm-info-icon:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.wcm-refresh-btn:active {
    transform: scale(0.95);
}

.wcm-view-link {
    color: #2271b1;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
}

.wcm-view-link:hover {
    color: #135e96;
}

/* Chart Section */
.wcm-chart-section {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 32px;
    margin: 32px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.wcm-chart-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.wcm-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.wcm-chart-header h2 {
    margin: 0;
    font-size: 18px;
}

.wcm-chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.wcm-chart-container {
    position: relative;
    height: 400px;
    margin-top: 20px;
}

/* Quick Actions */
.wcm-quick-actions {
    margin: 30px 0;
}

.wcm-quick-actions h2 {
    margin-bottom: 15px;
}

.wcm-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.wcm-action-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e1e5e9;
    border-radius: 16px;
    padding: 24px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.wcm-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.wcm-action-card:hover {
    transform: translateY(-4px);
    border-color: #667eea;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
    color: inherit;
}

.wcm-action-card:hover::before {
    opacity: 1;
}

.wcm-action-card .dashicons {
    font-size: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.wcm-action-card:hover .dashicons {
    transform: scale(1.1) rotate(5deg);
}

.wcm-action-card h3 {
    margin: 10px 0 5px;
    font-size: 16px;
}

.wcm-action-card p {
    margin: 0;
    color: #646970;
    font-size: 13px;
}

/* Recent Expenses */
.wcm-recent-expenses {
    margin: 30px 0;
}

.wcm-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.wcm-section-header h2 {
    margin: 0;
}

/* Expenses Table */
.wcm-expenses-table-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    overflow: hidden;
}

.wcm-expenses-table {
    margin: 0;
}

.wcm-expense-note {
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
    font-style: italic;
}

.wcm-category-badge {
    background: #f0f0f1;
    color: #50575e;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-created-date {
    font-size: 11px;
    color: #646970;
    margin-top: 3px;
}

/* Form Styles */
.wcm-form-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 30px;
    margin: 20px 0;
}

.wcm-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.wcm-form-group {
    display: flex;
    flex-direction: column;
}

.wcm-form-group-full {
    grid-column: 1 / -1;
}

.wcm-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1d2327;
}

.wcm-required {
    color: #d63638;
}

.wcm-input,
.wcm-textarea {
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.wcm-input:focus,
.wcm-textarea:focus {
    border-color: #2271b1;
    outline: none;
    box-shadow: 0 0 0 1px #2271b1;
}

.wcm-input.wcm-error,
.wcm-textarea.wcm-error {
    border-color: #d63638;
    box-shadow: 0 0 0 1px #d63638;
}

.wcm-input.wcm-error:focus,
.wcm-textarea.wcm-error:focus {
    border-color: #d63638;
    box-shadow: 0 0 0 1px #d63638;
}

.wcm-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.wcm-currency-symbol {
    position: absolute;
    left: 12px;
    color: #646970;
    font-weight: 600;
    z-index: 1;
}

.wcm-amount-input {
    padding-left: 30px;
}

.wcm-field-description {
    font-size: 12px;
    color: #646970;
    margin: 5px 0 0;
    font-style: italic;
}

.wcm-form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.wcm-submit-btn {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Quick Categories */
.wcm-quick-categories {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.wcm-quick-categories h3 {
    margin-top: 0;
    margin-bottom: 10px;
}

.wcm-section-description {
    color: #646970;
    font-size: 13px;
    margin-bottom: 15px;
}

.wcm-category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.wcm-category-btn {
    font-size: 12px;
    padding: 6px 12px;
}

.wcm-category-active {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}

/* Expense Templates */
.wcm-expense-templates {
    margin: 30px 0;
}

.wcm-templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.wcm-template-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wcm-template-card:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wcm-template-card .dashicons {
    font-size: 24px;
    color: #2271b1;
    margin-bottom: 8px;
}

.wcm-template-card h4 {
    margin: 8px 0 5px;
    font-size: 14px;
}

.wcm-template-card p {
    margin: 0;
    font-size: 12px;
    color: #646970;
}

/* Filters */
.wcm-filters {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.wcm-filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.wcm-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.wcm-filter-group label {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #1d2327;
}

.wcm-filter-group input,
.wcm-filter-group select {
    padding: 6px 10px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 13px;
}

/* Summary Stats */
.wcm-summary-stats {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.wcm-stat {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.wcm-stat-label {
    font-size: 12px;
    color: #646970;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #1d2327;
}

/* No Data State */
.wcm-no-data {
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
}

.wcm-no-data-icon {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 20px;
}

.wcm-no-data h3 {
    margin: 0 0 10px;
    color: #50575e;
}

.wcm-no-data p {
    color: #646970;
    margin-bottom: 20px;
}

.wcm-no-data-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Actions */
.wcm-actions {
    display: flex;
    gap: 8px;
}

.wcm-edit-btn,
.wcm-delete-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    padding: 4px 8px;
}

.wcm-delete-btn {
    color: #d63638;
    border-color: #d63638;
}

.wcm-delete-btn:hover {
    background: #d63638;
    color: #fff;
}

/* Pagination */
.wcm-pagination {
    text-align: center;
    margin: 20px 0;
}

.wcm-pagination .page-numbers {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    color: #2271b1;
}

.wcm-pagination .page-numbers:hover,
.wcm-pagination .page-numbers.current {
    background: #2271b1;
    color: #fff;
    border-color: #2271b1;
}

/* Modal */
.wcm-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.wcm-modal.wcm-modal-show {
    opacity: 1;
    visibility: visible;
}

.wcm-modal-content {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.wcm-modal.wcm-modal-show .wcm-modal-content {
    transform: scale(1) translateY(0);
}

.wcm-expense-modal-content {
    max-width: 700px;
}

.wcm-modal-header {
    padding: 24px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f1;
    margin-bottom: 24px;
}

.wcm-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #1d2327;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.wcm-modal-close {
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    color: #8e9aaf;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wcm-modal-close:hover {
    color: #d63638;
    background: rgba(214, 54, 56, 0.1);
    transform: scale(1.1);
}

.wcm-modal-body {
    padding: 0 24px 24px;
}

.wcm-modal-footer {
    padding: 20px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    border-top: 1px solid #f0f0f1;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 0 0 16px 16px;
}

.wcm-expense-preview {
    background: #f6f7f7;
    padding: 15px;
    border-radius: 4px;
    margin-top: 15px;
    font-size: 13px;
}

/* Expense History */
.wcm-expense-history {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.wcm-history-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.wcm-history-item:last-child {
    margin-bottom: 0;
}

.wcm-history-icon {
    width: 32px;
    height: 32px;
    background: #2271b1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 14px;
}

.wcm-history-title {
    font-weight: 600;
    color: #1d2327;
}

.wcm-history-date {
    font-size: 12px;
    color: #646970;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wcm-cards-container {
        grid-template-columns: 1fr;
    }
    
    .wcm-form-grid {
        grid-template-columns: 1fr;
    }
    
    .wcm-chart-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wcm-chart-controls {
        justify-content: center;
    }
    
    .wcm-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .wcm-filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wcm-filter-group {
        min-width: auto;
    }
    
    .wcm-summary-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .wcm-form-actions {
        flex-direction: column;
    }
    
    .wcm-section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wcm-no-data-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .wcm-modal-content {
        margin: 5% auto;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .wcm-form-container {
        padding: 20px;
    }

    .wcm-templates-grid {
        grid-template-columns: 1fr;
    }

    .wcm-category-buttons {
        justify-content: center;
    }
}

/* Loading States */
.wcm-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.wcm-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
    z-index: 10;
}

.wcm-spinner {
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: wcm-spin 0.8s linear infinite;
    margin-right: 8px;
}

@keyframes wcm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade in animation */
@keyframes wcm-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wcm-card,
.wcm-chart-section,
.wcm-action-card {
    animation: wcm-fadeIn 0.6s ease-out;
}

/* Pulse animation for refresh button */
@keyframes wcm-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.wcm-refresh-btn.wcm-loading {
    animation: wcm-pulse 1s ease-in-out infinite;
}

/* Modern Notifications */
.wcm-modern-notice {
    position: fixed;
    top: 32px;
    right: 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    padding: 16px 20px;
    min-width: 300px;
    max-width: 500px;
    z-index: 999999;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.wcm-modern-notice.wcm-notice-show {
    transform: translateX(0);
    opacity: 1;
}

.wcm-notice-icon {
    margin-right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.wcm-notice-success .wcm-notice-icon {
    background: linear-gradient(135deg, #00d4aa 0%, #00a32a 100%);
    color: #fff;
}

.wcm-notice-error .wcm-notice-icon {
    background: linear-gradient(135deg, #ff6b6b 0%, #d63638 100%);
    color: #fff;
}

.wcm-notice-info .wcm-notice-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.wcm-notice-icon .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.wcm-notice-content {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #1d2327;
    line-height: 1.4;
}

.wcm-notice-close {
    margin-left: 12px;
    cursor: pointer;
    color: #8e9aaf;
    transition: color 0.3s ease;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.wcm-notice-close:hover {
    color: #d63638;
    background: rgba(214, 54, 56, 0.1);
}

.wcm-notice-close .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Modal Form Styling */
.wcm-modal-quick-categories {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f1;
}

.wcm-modal-quick-categories .wcm-label {
    margin-bottom: 12px;
    display: block;
}

.wcm-modal-quick-categories .wcm-category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.wcm-modal-quick-categories .wcm-category-btn {
    font-size: 11px;
    padding: 6px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.wcm-modal-quick-categories .wcm-category-btn:hover,
.wcm-modal-quick-categories .wcm-category-btn.wcm-category-active {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
    transform: translateY(-1px);
}

/* Expenses Summary */
.wcm-expenses-summary {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
    border-radius: 12px;
    border: 1px solid #e1e5e9;
}

.wcm-summary-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.wcm-summary-label {
    font-size: 12px;
    color: #8e9aaf;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wcm-summary-value {
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Expenses Actions */
.wcm-expenses-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.wcm-expenses-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.wcm-expenses-actions .button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Action Card Button Styling */
.wcm-action-card.wcm-add-expense-trigger {
    cursor: pointer;
    border: none;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    text-align: center;
    font-family: inherit;
}

.wcm-action-card.wcm-add-expense-trigger:hover {
    transform: translateY(-4px);
    border-color: #667eea;
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}
