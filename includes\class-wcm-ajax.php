<?php
/**
 * AJAX functionality for Woo Cash Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WCM_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        // AJAX actions for logged-in users
        add_action('wp_ajax_wcm_delete_expense', array($this, 'delete_expense'));
        add_action('wp_ajax_wcm_get_chart_data', array($this, 'get_chart_data'));
        add_action('wp_ajax_wcm_refresh_dashboard', array($this, 'refresh_dashboard'));
    }
    
    /**
     * Delete expense via AJAX
     */
    public function delete_expense() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $expense_id = intval($_POST['expense_id']);
        
        if (!$expense_id) {
            wp_send_json_error('Invalid expense ID');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $result = $wpdb->delete(
            $table_name,
            array('id' => $expense_id),
            array('%d')
        );
        
        if ($result) {
            wp_send_json_success('Expense deleted successfully');
        } else {
            wp_send_json_error('Failed to delete expense');
        }
    }
    
    /**
     * Get chart data via AJAX
     */
    public function get_chart_data() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $period = sanitize_text_field($_POST['period']);
        
        switch ($period) {
            case 'yearly':
                $data = $this->get_yearly_data();
                break;
            case 'quarterly':
                $data = $this->get_quarterly_data();
                break;
            default:
                $data = $this->get_monthly_data();
                break;
        }
        
        wp_send_json_success($data);
    }
    
    /**
     * Refresh dashboard data via AJAX
     */
    public function refresh_dashboard() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wcm_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_die('Insufficient permissions');
        }
        
        $cash_data = $this->get_cash_data();
        
        wp_send_json_success($cash_data);
    }
    
    /**
     * Get cash data
     */
    private function get_cash_data() {
        $income = $this->get_total_income();
        $expenses = $this->get_total_expenses();
        $balance = $income - $expenses;
        
        return array(
            'income' => wc_price($income),
            'expenses' => wc_price($expenses),
            'balance' => wc_price($balance),
            'balance_raw' => $balance
        );
    }
    
    /**
     * Get total income from WooCommerce orders (HPOS Compatible)
     */
    private function get_total_income() {
        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled, use direct database query for better performance
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'"
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method for legacy stores
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get total expenses
     */
    private function get_total_expenses() {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $total = $wpdb->get_var("SELECT SUM(amount) FROM $table_name");
        
        return $total ? $total : 0;
    }
    
    /**
     * Get monthly data for charts
     */
    private function get_monthly_data() {
        $months = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $date = date('Y-m', strtotime("-$i months"));
            $months[] = date('M Y', strtotime("-$i months"));
            
            // Get income for this month
            $income_data[] = $this->get_monthly_income($date);
            
            // Get expenses for this month
            $expense_data[] = $this->get_monthly_expenses($date);
        }
        
        return array(
            'labels' => $months,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get yearly data for charts
     */
    private function get_yearly_data() {
        $years = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 5 years
        for ($i = 4; $i >= 0; $i--) {
            $year = date('Y', strtotime("-$i years"));
            $years[] = $year;
            
            // Get income for this year
            $income_data[] = $this->get_yearly_income($year);
            
            // Get expenses for this year
            $expense_data[] = $this->get_yearly_expenses($year);
        }
        
        return array(
            'labels' => $years,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get quarterly data for charts
     */
    private function get_quarterly_data() {
        $quarters = array();
        $income_data = array();
        $expense_data = array();
        
        // Get last 8 quarters
        for ($i = 7; $i >= 0; $i--) {
            $quarter_start = date('Y-m-d', strtotime("-$i quarters"));
            $quarter = 'Q' . ceil(date('n', strtotime($quarter_start)) / 3) . ' ' . date('Y', strtotime($quarter_start));
            $quarters[] = $quarter;
            
            // Get income for this quarter
            $income_data[] = $this->get_quarterly_income($quarter_start);
            
            // Get expenses for this quarter
            $expense_data[] = $this->get_quarterly_expenses($quarter_start);
        }
        
        return array(
            'labels' => $quarters,
            'income' => $income_data,
            'expenses' => $expense_data
        );
    }
    
    /**
     * Get monthly income (HPOS Compatible)
     */
    private function get_monthly_income($month) {
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get monthly expenses
     */
    private function get_monthly_expenses($month) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = $month . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
    
    /**
     * Get yearly income (HPOS Compatible)
     */
    private function get_yearly_income($year) {
        $start_date = $year . '-01-01';
        $end_date = $year . '-12-31';

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get yearly expenses
     */
    private function get_yearly_expenses($year) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = $year . '-01-01';
        $end_date = $year . '-12-31';
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
    
    /**
     * Get quarterly income (HPOS Compatible)
     */
    private function get_quarterly_income($quarter_start) {
        $start_date = date('Y-m-d', strtotime($quarter_start));
        $end_date = date('Y-m-d', strtotime($quarter_start . ' +3 months -1 day'));

        // Use HPOS compatible method
        if (class_exists('\Automattic\WooCommerce\Utilities\OrderUtil') &&
            \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled()) {

            // HPOS is enabled
            global $wpdb;

            $orders_table = $wpdb->prefix . 'wc_orders';
            $total = $wpdb->get_var($wpdb->prepare(
                "SELECT SUM(total_amount) FROM {$orders_table}
                 WHERE status IN ('wc-completed', 'wc-processing')
                 AND type = 'shop_order'
                 AND date_created_gmt BETWEEN %s AND %s",
                $start_date . ' 00:00:00',
                $end_date . ' 23:59:59'
            ));

            return $total ? floatval($total) : 0;

        } else {
            // Fallback to traditional method
            $orders = wc_get_orders(array(
                'status' => array('completed', 'processing'),
                'date_created' => $start_date . '...' . $end_date,
                'limit' => -1,
                'return' => 'ids'
            ));

            $total = 0;
            foreach ($orders as $order_id) {
                $order = wc_get_order($order_id);
                if ($order) {
                    $total += $order->get_total();
                }
            }

            return $total;
        }
    }
    
    /**
     * Get quarterly expenses
     */
    private function get_quarterly_expenses($quarter_start) {
        global $wpdb;
        $table_name = $wpdb->prefix . WCM_TABLE_NAME;
        
        $start_date = date('Y-m-d', strtotime($quarter_start));
        $end_date = date('Y-m-d', strtotime($quarter_start . ' +3 months -1 day'));
        
        $total = $wpdb->get_var($wpdb->prepare(
            "SELECT SUM(amount) FROM $table_name WHERE expense_date BETWEEN %s AND %s",
            $start_date,
            $end_date
        ));
        
        return $total ? $total : 0;
    }
}
